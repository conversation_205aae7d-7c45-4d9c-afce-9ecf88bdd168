import prisma from '../lib/prisma';
import { AccountType, TransactionType, RecurrenceFrequency } from '@prisma/client';

async function seed() {
  console.log('🌱 Seeding database with sample data...\n');

  try {
    // Clear existing data (in reverse order of dependencies)
    await prisma.transactionTag.deleteMany();
    await prisma.transactionMember.deleteMany();
    await prisma.accountMember.deleteMany();
    await prisma.goalMember.deleteMany();
    await prisma.recurringTransactionMember.deleteMany();
    await prisma.accountBalanceHistory.deleteMany();
    await prisma.goalMilestone.deleteMany();
    await prisma.transaction.deleteMany();
    await prisma.recurringTransaction.deleteMany();
    await prisma.budget.deleteMany();
    await prisma.goal.deleteMany();
    await prisma.tag.deleteMany();
    await prisma.category.deleteMany();
    await prisma.account.deleteMany();
    await prisma.familyMember.deleteMany();

    console.log('🗑️  Cleared existing data');

    // Create Family Members
    const familyMembers = await Promise.all([
      prisma.familyMember.create({
        data: { name: 'João Silva', color: '#3B82F6' }
      }),
      prisma.familyMember.create({
        data: { name: 'Maria Silva', color: '#EF4444' }
      }),
      prisma.familyMember.create({
        data: { name: 'Pedro Silva', color: '#10B981' }
      })
    ]);
    console.log('👥 Created family members');

    // Create Accounts
    const accounts = await Promise.all([
      prisma.account.create({
        data: {
          name: 'Conta Corrente Banco do Brasil',
          type: AccountType.CHECKING,
          currency: 'BRL',
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Poupança Caixa',
          type: AccountType.SAVINGS,
          currency: 'BRL',
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Cartão Nubank',
          type: AccountType.CREDIT_CARD,
          currency: 'BRL',
          creditLimit: 5000.00,
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Conta USD',
          type: AccountType.CHECKING,
          currency: 'USD',
          exchangeRate: 5.20,
          includeInTotal: true
        }
      }),
      prisma.account.create({
        data: {
          name: 'Apartamento',
          type: AccountType.ASSETS,
          currency: 'BRL',
          includeInTotal: false
        }
      })
    ]);
    console.log('🏦 Created accounts');

    // Associate accounts with family members
    await Promise.all([
      prisma.accountMember.create({
        data: { accountId: accounts[0].id, familyMemberId: familyMembers[0].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[0].id, familyMemberId: familyMembers[1].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[1].id, familyMemberId: familyMembers[0].id }
      }),
      prisma.accountMember.create({
        data: { accountId: accounts[2].id, familyMemberId: familyMembers[1].id }
      })
    ]);

    // Create Categories
    const categories = await Promise.all([
      prisma.category.create({
        data: { name: 'Alimentação', color: '#10B981' }
      }),
      prisma.category.create({
        data: { name: 'Transporte', color: '#F59E0B' }
      }),
      prisma.category.create({
        data: { name: 'Moradia', color: '#8B5CF6' }
      }),
      prisma.category.create({
        data: { name: 'Renda', color: '#059669' }
      })
    ]);

    // Create Subcategories
    const subcategories = await Promise.all([
      prisma.category.create({
        data: { name: 'Restaurantes', color: '#059669', parentId: categories[0].id }
      }),
      prisma.category.create({
        data: { name: 'Supermercado', color: '#047857', parentId: categories[0].id }
      }),
      prisma.category.create({
        data: { name: 'Combustível', color: '#D97706', parentId: categories[1].id }
      }),
      prisma.category.create({
        data: { name: 'Aluguel', color: '#7C3AED', parentId: categories[2].id }
      })
    ]);
    console.log('📂 Created categories and subcategories');

    // Create Tags
    const tags = await Promise.all([
      prisma.tag.create({
        data: { name: 'viagem2024', color: '#8B5CF6' }
      }),
      prisma.tag.create({
        data: { name: 'trabalho', color: '#3B82F6' }
      }),
      prisma.tag.create({
        data: { name: 'emergencia', color: '#EF4444' }
      })
    ]);
    console.log('🏷️  Created tags');

    // Create Transactions with installments
    const transactions = await Promise.all([
      prisma.transaction.create({
        data: {
          description: 'Salário dezembro',
          totalAmount: 5000.00,
          totalInstallments: 1,
          transactionDate: new Date('2024-12-01'),
          type: TransactionType.INCOME,
          accountId: accounts[0].id,
          categoryId: categories[3].id,
          installments: {
            create: [{
              installmentNumber: 1,
              amount: 5000.00,
              dueDate: new Date('2024-12-01'),
              isPaid: true,
              paidAt: new Date('2024-12-01'),
              description: 'Salário dezembro - Parcela 1/1'
            }]
          },
          tags: {
            create: [
              { tagId: tags[1].id } // trabalho
            ]
          }
        }
      }),
      prisma.transaction.create({
        data: {
          description: 'Almoço no restaurante',
          totalAmount: 45.50,
          totalInstallments: 1,
          transactionDate: new Date('2024-12-02'),
          type: TransactionType.EXPENSE,
          accountId: accounts[0].id,
          categoryId: subcategories[0].id,
          installments: {
            create: [{
              installmentNumber: 1,
              amount: 45.50,
              dueDate: new Date('2024-12-02'),
              isPaid: true,
              paidAt: new Date('2024-12-02'),
              description: 'Almoço no restaurante - Parcela 1/1'
            }]
          },
          tags: {
            create: [
              { tagId: tags[0].id } // viagem2024
            ]
          }
        }
      }),
      prisma.transaction.create({
        data: {
          description: 'Compras no supermercado',
          totalAmount: 180.75,
          totalInstallments: 1,
          transactionDate: new Date('2024-12-03'),
          type: TransactionType.EXPENSE,
          accountId: accounts[2].id,
          categoryId: subcategories[1].id,
          installments: {
            create: [{
              installmentNumber: 1,
              amount: 180.75,
              dueDate: new Date('2024-12-03'),
              isPaid: false,
              description: 'Compras no supermercado - Parcela 1/1'
            }]
          }
        }
      }),
      prisma.transaction.create({
        data: {
          description: 'Transferência para poupança',
          totalAmount: 1000.00,
          totalInstallments: 1,
          transactionDate: new Date('2024-12-04'),
          type: TransactionType.TRANSFER,
          accountId: accounts[0].id,
          destinationAccountId: accounts[1].id,
          installments: {
            create: [{
              installmentNumber: 1,
              amount: 1000.00,
              dueDate: new Date('2024-12-04'),
              isPaid: true,
              paidAt: new Date('2024-12-04'),
              description: 'Transferência para poupança - Parcela 1/1'
            }]
          },
          tags: {
            create: [
              { tagId: tags[2].id } // emergencia
            ]
          }
        }
      })
    ]);
    console.log('💰 Created transactions');

    // Create Goals
    const goals = await Promise.all([
      prisma.goal.create({
        data: {
          name: 'Viagem para Europa',
          targetAmount: 15000.00,
          currentAmount: 2500.00,
          targetDate: new Date('2025-06-01'),
          milestones: {
            create: [
              {
                name: 'Primeira etapa - Passagens',
                targetAmount: 5000.00,
                targetDate: new Date('2025-02-01')
              },
              {
                name: 'Segunda etapa - Hospedagem',
                targetAmount: 10000.00,
                targetDate: new Date('2025-04-01')
              }
            ]
          }
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Reserva de Emergência',
          targetAmount: 30000.00,
          currentAmount: 8500.00,
          targetDate: new Date('2025-12-31')
        }
      })
    ]);
    console.log('🎯 Created goals with milestones');

    // Create Budgets
    await Promise.all([
      prisma.budget.create({
        data: {
          plannedAmount: 800.00,
          month: 12,
          year: 2024,
          categoryId: categories[0].id,
          familyMemberId: familyMembers[0].id
        }
      }),
      prisma.budget.create({
        data: {
          plannedAmount: 400.00,
          month: 12,
          year: 2024,
          categoryId: categories[1].id,
          familyMemberId: familyMembers[1].id
        }
      }),
      prisma.budget.create({
        data: {
          plannedAmount: 1500.00,
          month: 12,
          year: 2024,
          categoryId: categories[2].id
        }
      })
    ]);
    console.log('📊 Created budgets');

    // Create Recurring Transactions
    await Promise.all([
      prisma.recurringTransaction.create({
        data: {
          description: 'Salário mensal',
          fixedAmount: 5000.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.INCOME,
          accountId: accounts[0].id,
          categoryId: categories[3].id
        }
      }),
      prisma.recurringTransaction.create({
        data: {
          description: 'Aluguel',
          fixedAmount: 1200.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: accounts[0].id,
          categoryId: subcategories[3].id
        }
      })
    ]);
    console.log('🔄 Created recurring transactions');

    // Create Account Balance History
    const today = new Date();
    await Promise.all([
      prisma.accountBalanceHistory.create({
        data: {
          accountId: accounts[0].id,
          balance: 3250.75,
          balanceDate: today
        }
      }),
      prisma.accountBalanceHistory.create({
        data: {
          accountId: accounts[1].id,
          balance: 8500.00,
          balanceDate: today
        }
      }),
      prisma.accountBalanceHistory.create({
        data: {
          accountId: accounts[2].id,
          balance: -180.75,
          balanceDate: today
        }
      })
    ]);
    console.log('📈 Created balance history');

    console.log('\n🎉 Database seeded successfully!');
    console.log(`
📊 Summary:
- ${familyMembers.length} family members
- ${accounts.length} accounts
- ${categories.length + subcategories.length} categories
- ${tags.length} tags
- ${transactions.length} transactions
- ${goals.length} goals
- 3 budgets
- 2 recurring transactions
- 3 balance history records
    `);

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run seed if this file is executed directly
if (require.main === module) {
  seed()
    .then(() => {
      console.log('✅ Seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export default seed;
